import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import { getTheme, saveUiSettingsInCookies } from "./uiCookies";

// Mock getCookieValue
vi.mock("./getCookieValue", () => ({
  default: vi.fn(),
}));

import getCookieValue from "./getCookieValue";

describe("getTheme", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("returns 'light' when cookieHeader is null", () => {
    const result = getTheme(null);
    expect(result).toBe("light");
  });

  it("returns 'light' when cookieHeader is empty string", () => {
    const result = getTheme("");
    expect(result).toBe("light");
  });

  it("returns theme from valid ui-settings cookie", () => {
    const mockSettings = { theme: "dark" };
    const encodedSettings = encodeURIComponent(JSON.stringify(mockSettings));

    vi.mocked(getCookieValue).mockReturnValue(encodedSettings);

    const result = getTheme("ui-settings=encoded-value");

    expect(getCookieValue).toHaveBeenCalledWith("ui-settings=encoded-value", "ui-settings");
    expect(result).toBe("dark");
  });

  it("returns 'light' when getCookieValue returns undefined", () => {
    vi.mocked(getCookieValue).mockReturnValue(undefined);

    const result = getTheme("some-cookie-header");

    expect(result).toBe("light");
  });

  it("returns 'light' when getCookieValue returns empty string", () => {
    vi.mocked(getCookieValue).mockReturnValue("");

    const result = getTheme("some-cookie-header");

    expect(result).toBe("light");
  });

  it("returns 'light' when cookie contains invalid JSON", () => {
    vi.mocked(getCookieValue).mockReturnValue("invalid-json");

    const result = getTheme("ui-settings=invalid-json");

    expect(result).toBe("light");
  });

  it("returns undefined when cookie contains valid JSON but no theme property", () => {
    const mockSettings = { otherProperty: "value" };
    const encodedSettings = encodeURIComponent(JSON.stringify(mockSettings));

    vi.mocked(getCookieValue).mockReturnValue(encodedSettings);

    const result = getTheme("ui-settings=encoded-value");

    expect(result).toBeUndefined();
  });

  it("returns theme when cookie contains valid JSON with theme property", () => {
    const mockSettings = { theme: "system", otherProperty: "value" };
    const encodedSettings = encodeURIComponent(JSON.stringify(mockSettings));

    vi.mocked(getCookieValue).mockReturnValue(encodedSettings);

    const result = getTheme("ui-settings=encoded-value");

    expect(result).toBe("system");
  });

  it("handles URL decoding errors gracefully", () => {
    // Return a string that would cause decodeURIComponent to throw
    vi.mocked(getCookieValue).mockReturnValue("%E0%A4%A");

    const result = getTheme("ui-settings=malformed-encoded-value");

    expect(result).toBe("light");
  });

  it("handles JSON parsing errors gracefully", () => {
    // Return a properly encoded string that contains invalid JSON
    const invalidJson = encodeURIComponent("{invalid json}");
    vi.mocked(getCookieValue).mockReturnValue(invalidJson);

    const result = getTheme("ui-settings=encoded-invalid-json");

    expect(result).toBe("light");
  });
});

describe("saveUiSettingsInCookies", () => {
  let mockCookieSetter: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    mockCookieSetter = vi.fn();
    // Mock document.cookie setter
    Object.defineProperty(document, "cookie", {
      set: mockCookieSetter,
      configurable: true,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it("saves theme settings in cookie with correct format", () => {
    const settings = { theme: "dark" };

    saveUiSettingsInCookies(settings);

    const expectedValue = `ui-settings=${encodeURIComponent(
      JSON.stringify(settings)
    )}; path=/; max-age=${60 * 60 * 24 * 365}`;

    expect(mockCookieSetter).toHaveBeenCalledWith(expectedValue);
  });

  it("saves light theme settings in cookie", () => {
    const settings = { theme: "light" };

    saveUiSettingsInCookies(settings);

    const expectedValue = `ui-settings=${encodeURIComponent(
      JSON.stringify(settings)
    )}; path=/; max-age=${60 * 60 * 24 * 365}`;

    expect(mockCookieSetter).toHaveBeenCalledWith(expectedValue);
  });

  it("properly encodes special characters in theme values", () => {
    const settings = { theme: "custom-theme with spaces & symbols" };

    saveUiSettingsInCookies(settings);

    const expectedValue = `ui-settings=${encodeURIComponent(
      JSON.stringify(settings)
    )}; path=/; max-age=${60 * 60 * 24 * 365}`;

    expect(mockCookieSetter).toHaveBeenCalledWith(expectedValue);
  });
});